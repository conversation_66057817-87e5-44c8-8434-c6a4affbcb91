---
type: "agent_requested"
description: "Example description"
---

# Data Handling and Entity Patterns Rules

## Temporary Entity Pattern

### Preferred Approach

- **Use temporary entity pattern** where items can be added without input and remain temporary until required fields are filled
- Follow the pattern established in the products page implementation
- Allow users to create placeholder items that become "real" once validated

### Implementation Pattern

```typescript
// Temporary entity example
const createTemporaryItem = () => {
  return {
    id: crypto.randomUUID(),
    name: "", // Empty until filled
    isTemporary: true,
    // ... other fields
  };
};

// Convert to permanent when validated
const makePermanent = (item: TemporaryItem) => {
  if (isValid(item)) {
    return { ...item, isTemporary: false };
  }
};
```

## User Interaction Patterns

### Long-Click Interactions

- **Prefer long-click interactions** to trigger modals for adding notes to products
- Use 500ms threshold for long-press detection
- Implement with custom `useLongPress` hook pattern

### Note Display Pattern

- **Products with notes should be displayed as separate list items** in order lists
- Show notes below product names in italic styling
- Each note variant creates a separate manageable line item

### Example Implementation

```typescript
// Long-press for notes
const longPressEvents = useLongPress({
  onLongPress: () => openNoteModal(product),
  onClick: () => addProduct(product),
  threshold: 500,
});

// Note display in orders
{product.name}
{note && <p className="text-sm italic text-gray-600">{note}</p>}
```
