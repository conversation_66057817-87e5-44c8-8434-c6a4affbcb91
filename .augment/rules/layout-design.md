---
type: "agent_requested"
description: "Example description"
---

# Layout and Design Rules

## Design System Consistency

### Modern Layout Patterns

- **Use Flexbox over CSS Grid** for most layout scenarios
- **Implement consistent header patterns** across all views
- **Maintain unified color schemes** and spacing throughout the application

### Header Pattern

```typescript
// Consistent header structure
<div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4 flex-shrink-0">
  <div className="flex items-center justify-between">
    <div>
      <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
        {title}
      </h1>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        {subtitle}
      </p>
    </div>
    <button className="px-4 py-2 text-sm font-medium...">
      {actionLabel}
    </button>
  </div>
</div>
```

## Layout Architecture

### Two-Column Layouts

- **Use consistent two-column patterns** for data management interfaces
- **Left column**: Primary data/actions (fixed or flexible width)
- **Right column**: Secondary data/details (flexible width)
- **Proper overflow handling**: Each column manages its own scrolling

### Full-Height Layouts

```typescript
// Standard full-height layout pattern
<div className="fixed top-0 left-0 w-full h-full bg-gray-50 dark:bg-gray-900">
  <div className="h-full flex flex-col">
    <header className="flex-shrink-0">Header</header>
    <main className="flex-1 flex overflow-hidden">
      <aside className="w-80 flex flex-col">Left Column</aside>
      <section className="flex-1 flex flex-col">Right Column</section>
    </main>
  </div>
</div>
```

## Component Separation

### Avoid Complex CSS Modules

- **Prefer Tailwind CSS classes** over complex CSS modules
- **Extract reusable components** instead of relying on CSS Grid layouts
- **Use semantic HTML structure** with proper accessibility

### Custom Layout Components

- **Create dedicated layout components** for complex interfaces
- **Avoid reusing layouts** designed for different purposes
- **Implement proper responsive behavior** with Tailwind breakpoints
