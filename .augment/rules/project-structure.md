---
type: "agent_requested"
description: "Example description"
---

# Project Structure and Architecture Rules

## Monorepo Organization

### Directory Structure

- **apps/frontend**: React/Vite frontend application
- **apps/backend**: NestJS backend application
- **packages/shared**: Shared types, models, and utilities
- **Maintain separation** between frontend and backend concerns

### Shared Code Patterns

- **Define types in packages/shared/model**
- **Share business logic** through packages/shared/data
- **Use consistent import paths** with proper TypeScript path mapping

## Component Architecture

### Feature-Based Organization

```
src/
├── kassierer/           # Kassierer (cashier) feature
│   ├── table/          # Table selection
│   ├── order/          # Order management
│   ├── invoice/        # Invoice/payment
│   └── ui/             # Shared kassierer components
├── admin/              # Admin feature
│   ├── Order/          # Order management
│   ├── Products/       # Product management
│   └── Tables/         # Table management
└── shared/             # Cross-feature components
```

### Component Naming

- **Use PascalCase** for component files and directories
- **Use descriptive names** that indicate functionality
- **Group related components** in feature directories

## Data Layer Architecture

### YJS CRDT Integration

- **Maintain YJS document structure** for real-time collaboration
- **Use established store patterns** for YJS integration
- **Keep YService.ts files unchanged** during refactoring

### Store Organization

```typescript
// Store pattern for each entity
export const useEntityName = create<EntityStore>((set) => ({
  entities: [],
  createEntity: () => {},
  updateEntity: () => {},
  deleteEntity: () => {},
  initialize: () => {
    // YJS integration
  },
}));
```
