{"name": "kassierer-print-server", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --fix", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@tailwindcss/vite": "^4.1.13", "ag-grid-react": "^34.2.0", "axios": "^1.11.0", "framer-motion": "^12.23.12", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.7", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2", "tailwindcss": "^4.1.13", "y-indexeddb": "^9.0.12", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@kassierer/shared": "1.0.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}